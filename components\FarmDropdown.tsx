import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { Farm } from '@/types/farm';
import { useTranslation } from '@/hooks/useTranslation';
import GenericDropdown, { DropdownItem } from '@/components/GenericDropdown';
import { MapPin } from 'lucide-react-native';
import { colors } from '@/constants/colors';

interface FarmDropdownProps {
  onFarmSelect: (farmId: string, farmName: string) => void;
  selectedFarmId?: string;
  error?: string;
}

const FarmDropdown: React.FC<FarmDropdownProps> = ({
  onFarmSelect,
  selectedFarmId,
  error,
}) => {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const { farms, fetchFarms, isLoading } = useFarmStore();
  const [farmItems, setFarmItems] = useState<DropdownItem[]>([]);

  useEffect(() => {
    if (user) {
      fetchFarms(user.id);
    }
  }, [user]);

  useEffect(() => {
    // Convert farms to dropdown items
    const items = farms.map((farm) => ({
      id: farm.id,
      label: farm.name,
      description: farm.location,
      icon: <MapPin size={20} color={colors.primary} />
    }));
    setFarmItems(items);
  }, [farms]);

  // Handle farm selection
  const handleSelect = (farmId: string) => {
    const selectedFarm = farms.find(farm => farm.id === farmId);
    if (selectedFarm) {
      onFarmSelect(selectedFarm.id, selectedFarm.name);
    }
  };

  return (
    <GenericDropdown
      placeholder={t('farms.selectFarmPlaceholder')}
      items={farmItems}
      value={selectedFarmId || ''}
      onSelect={handleSelect}
      modalTitle={t('farms.selectFarm')}
      searchPlaceholder={t('farms.searchFarms')}
      error={error}
    />
  );
};

export default FarmDropdown;