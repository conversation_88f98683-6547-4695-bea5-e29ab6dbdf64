import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { AnimalRecord, RecordType } from '@/types/record';
import { v4 as uuidv4 } from 'uuid';
import { collection, addDoc, updateDoc, deleteDoc, doc, getDocs, query, where, getDoc } from 'firebase/firestore';
import { firestore } from '@/config/firebase';
import { useAnimalStore } from '@/store/animal-store';
interface RecordState {
  records: AnimalRecord[];
  isLoading: boolean;
  error: string | null;
}

interface RecordStore extends RecordState {
  fetchRecords: (animalId?: string) => Promise<void>;
  addRecord: (record: Omit<AnimalRecord, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>;
  updateRecord: (id: string, updates: Partial<AnimalRecord>) => Promise<void>;
  deleteRecord: (id: string) => Promise<void>;
  getRecordById: (id: string) => AnimalRecord | undefined;
  getRecordsForAnimal: (animalId: string) => AnimalRecord[];
  clearRecords: () => void;
}

export const useRecordStore = create<RecordStore>()(
  persist(
    (set, get) => ({
      records: [],
      isLoading: false,
      error: null,

      fetchRecords: async (animalId) => {
        set({ isLoading: true, error: null });
        try {
          const fetchedRecords: AnimalRecord[] = [];
      
          if (animalId) {
            // Get the animal's farmId
            const animalStore = useAnimalStore.getState();
            const animal = animalStore.animals.find(a => a.id === animalId);
            
            if (!animal || !animal.farmId) {
              throw new Error('Animal not found or missing farmId');
            }
      
            // Fetch records from the animal's subcollection
            const farmId = animal.farmId;
            const animalRef = doc(firestore, 'farms', farmId, 'animals', animalId);
            const recordsCollectionRef = collection(animalRef, 'records');
            const querySnapshot = await getDocs(recordsCollectionRef);
      
            querySnapshot.forEach((doc) => {
              const data = doc.data();
              // Ensure all required fields are present
              if (data.animalId && data.date && data.type) {
                fetchedRecords.push({
                  id: doc.id,
                  ...data,
                  // Ensure these fields exist with defaults if missing
                  title: data.title || '',
                  description: data.description || '',
                  symptoms: data.symptoms || [],
                  createdAt: data.createdAt || Date.now(),
                  updatedAt: data.updatedAt || Date.now()
                } as AnimalRecord);
              }
            });
          } else {
            // Fetch all records across all animals that belong to the current user
            // Get all animals from the animal store (which should only contain user's animals)
            const { animals } = useAnimalStore.getState();

            // For each animal, get all records
            for (const animal of animals) {
              if (!animal.farmId) continue;

              const farmRef = doc(firestore, 'farms', animal.farmId);
              const animalRef = doc(farmRef, 'animals', animal.id);
              const recordsCollectionRef = collection(animalRef, 'records');

              try {
                const recordsSnapshot = await getDocs(recordsCollectionRef);

                recordsSnapshot.forEach((recordDoc) => {
                  const data = recordDoc.data();
                  if (data.animalId && data.date && data.type) {
                    fetchedRecords.push({
                      id: recordDoc.id,
                      ...data,
                      animalId: animal.id, // Ensure animalId is set
                      title: data.title || '',
                      description: data.description || '',
                      symptoms: data.symptoms || [],
                      createdAt: data.createdAt || Date.now(),
                      updatedAt: data.updatedAt || Date.now()
                    } as AnimalRecord);
                  }
                });
              } catch (error) {
                console.error(`Error fetching records for animal ${animal.id}:`, error);
                // Continue with other animals even if one fails
              }
            }
          }
      
      
          set({
            records: fetchedRecords,
            isLoading: false
          });
        } catch (error) {
          console.error('Error fetching records:', error);
          set({
            error: 'Failed to fetch records. Please try again.',
            isLoading: false
          });
        }
      },

      addRecord: async (recordData) => {
        set({ isLoading: true, error: null });
        try {
          const timestamp = Date.now();
          const newRecord = {
            ...recordData,
            createdAt: timestamp,
            updatedAt: timestamp,
          };
      
          // Get the animal's farmId
          const animalId = recordData.animalId;
          if (!animalId) {
            throw new Error('Animal ID is required');
          }
      
          // Find the animal to get its farmId
          const animalStore = useAnimalStore.getState();
          const animal = animalStore.animals.find(a => a.id === animalId);
          
          if (!animal || !animal.farmId) {
            throw new Error('Animal not found or missing farmId');
          }
      
          // Add to Firestore as subcollection under animal
          const farmId = animal.farmId;
          const animalRef = doc(firestore, 'farms', farmId, 'animals', animalId);
          const recordsCollectionRef = collection(animalRef, 'records');
          const docRef = await addDoc(recordsCollectionRef, newRecord);
      
          const addedRecord: AnimalRecord = {
            id: docRef.id,
            ...newRecord,
          };
      
          set(state => ({
            records: [...state.records, addedRecord],
            isLoading: false
          }));
      
          return docRef.id;
        } catch (error) {
          console.error('Error adding record:', error);
          set({
            error: 'Failed to add record. Please try again.',
            isLoading: false
          });
          return '';
        }
      },

      updateRecord: async (id, updates) => {
        set({ isLoading: true, error: null });
        try {
          // Find the record to get animalId
          const currentRecord = get().records.find(r => r.id === id);
          if (!currentRecord) {
            throw new Error('Record not found');
          }
      
          const animalId = currentRecord.animalId;
          
          // Get the animal to find its farmId
          const animalStore = useAnimalStore.getState();
          const animal = animalStore.animals.find(a => a.id === animalId);
          
          if (!animal || !animal.farmId) {
            throw new Error('Animal not found or missing farmId');
          }
      
          const farmId = animal.farmId;
          const recordRef = doc(firestore, 'farms', farmId, 'animals', animalId, 'records', id);
          
          const updatedRecord = {
            ...updates,
            updatedAt: Date.now()
          };
      
          await updateDoc(recordRef, updatedRecord);
      
          set(state => ({
            records: state.records.map(record =>
              record.id === id ? { ...record, ...updatedRecord } : record
            ),
            isLoading: false
          }));
        } catch (error) {
          console.error('Error updating record:', error);
          set({
            error: 'Failed to update record. Please try again.',
            isLoading: false
          });
        }
      },

      deleteRecord: async (id) => {
        set({ isLoading: true, error: null });
        try {
          // Find the record to get animalId
          const currentRecord = get().records.find(r => r.id === id);
          if (!currentRecord) {
            throw new Error('Record not found');
          }
      
          const animalId = currentRecord.animalId;
          
          // Get the animal to find its farmId
          const animalStore = useAnimalStore.getState();
          const animal = animalStore.animals.find(a => a.id === animalId);
          
          if (!animal || !animal.farmId) {
            throw new Error('Animal not found or missing farmId');
          }
      
          const farmId = animal.farmId;
          await deleteDoc(doc(firestore, 'farms', farmId, 'animals', animalId, 'records', id));
      
          set(state => ({
            records: state.records.filter(record => record.id !== id),
            isLoading: false
          }));
        } catch (error) {
          console.error('Error deleting record:', error);
          set({
            error: 'Failed to delete record. Please try again.',
            isLoading: false
          });
        }
      },

      getRecordById: (id) => {
        return get().records.find(record => record.id === id);
      },

      getRecordsForAnimal: (animalId) => {
        return get().records.filter(record => record.animalId === animalId);
      },

      clearRecords: () => {
        set({ records: [], error: null });
      }
    }),
    {
      name: 'record-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);






