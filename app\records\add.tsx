import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
  Switch,
  TextInput,
} from 'react-native';
import { useRouter, Stack, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { useAnimalStore } from '@/store/animal-store';
import { useRecordStore } from '@/store/record-store';
import { useTranslation } from '@/hooks/useTranslation';
import { useAudioFeedback } from '@/hooks/useAudioFeedback';
import { useThemeColors } from '@/hooks/useThemeColors';
import Toast from 'react-native-toast-message';

import DatePickerInput from '@/components/DatePickerInput';
import Input from '@/components/Input';
import AnimalSearchDropdown from '@/components/AnimalSearchDropdown';
import SpeciesFilterRow from '@/components/SpeciesFilterRow';
import GenericDropdown, { DropdownItem } from '@/components/GenericDropdown';
import FarmFilterDropdown from '@/components/FarmFilterDropdown';
import { getRecordOptions } from '@/constants/recordOptions';
import Button from '@/components/Button';
import {
  Calendar,
  ArrowLeft,
  FileText,
  Stethoscope,
  Syringe,
  Pill,
  Scissors,
  Baby,
  Save,
  FileType,
  Clock,
  Check,
  Mic,
  MicOff,
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { RecordType } from '@/types/record';
import { HEALTH_CHECK_INTERVAL, VACCINATION_INTERVAL } from '@/utils/healthStatus';
import { startSpeechRecognition, stopSpeechRecognition, setCurrentField } from '@/services/speech-service';
import { getStoredLanguage, getSpeechLanguageCode } from '@/services/language-service';
import { formatDate } from '@/utils/date-utils';

// Record type dropdown items
const getRecordTypeItems = (t: (key: string) => string, themedColors: ReturnType<typeof useThemeColors>): DropdownItem[] => [
  {
    id: RecordType.VACCINATION,
    label: t('records.vaccination'),
    icon: <Syringe size={32} color={themedColors.primary} />,
    description: t('records.vaccinationsDescription')
  },
  {
    id: RecordType.MEDICATION,
    label: t('records.medications'),
    icon: <Pill size={32} color={themedColors.primary} />,
    description: t('records.medicationsDescription')
  },
  {
    id: RecordType.SURGERY,
    label: t('records.surgery'),
    icon: <Scissors size={32} color={themedColors.primary} />,
    description: t('records.surgeriesDescription')
  },
  {
    id: RecordType.GENERAL,
    label: t('records.checkup'),
    icon: <Stethoscope size={32} color={themedColors.primary} />,
    description: t('records.checkupsDescription')
  },
  {
    id: RecordType.BIRTH,
    label: t('records.births'),
    icon: <Baby size={32} color={themedColors.primary} />,
    description: t('records.birthsDescription')
  },
];

// Helper function to get the appropriate label for record details based on record type
const getRecordDetailsLabel = (recordType: string, t: (key: string) => string): string => {
  switch (recordType) {
    case RecordType.VACCINATION:
      return t('records.vaccineName');
    case RecordType.MEDICATION:
      return t('records.medicineName');
    case RecordType.SURGERY:
      return t('records.surgeryType');
    case RecordType.BIRTH:
      return t('records.birthType');
    case RecordType.GENERAL:
      return t('records.checkupType');
    default:
      return t('records.details');
  }
};

export default function AddRecordScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const animalId = params.animalId as string;

  const { animals } = useAnimalStore();
  const { addRecord } = useRecordStore();
  const { t, language } = useTranslation();
  const { playFeedback } = useAudioFeedback();
  const themedColors = useThemeColors();

  const [selectedAnimal, setSelectedAnimal] = useState(animalId || '');
  const [recordDate, setRecordDate] = useState(new Date());
  const [recordType, setRecordType] = useState<string>('');
  const [nextDueDate, setNextDueDate] = useState<Date | null>(null);
  const [notes, setNotes] = useState('');
  const [practitioner, setPractitioner] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSelfTreatment, setIsSelfTreatment] = useState(false);

  // Add voice recording state
  const [isRecordingNotes, setIsRecordingNotes] = useState(false);
  const [isProcessingSpeech, setIsProcessingSpeech] = useState(false);

  // Add state for practitioner voice recording
  const [isRecordingPractitioner, setIsRecordingPractitioner] = useState(false);

  // If an animal is pre-selected, set its species as the selected category
  const preSelectedAnimal = animals.find(animal => animal.id === animalId);
  const [selectedSpecies, setSelectedSpecies] = useState<string | null>(
    preSelectedAnimal ? preSelectedAnimal.species : 'Cow' // Set default to 'Cow'
  );
  const [selectedOption, setSelectedOption] = useState<string>('');
  const [selectedFarmId, setSelectedFarmId] = useState<string | null>(null);

  // Filter animals by selected species and farm
  const filteredAnimals = animals.filter(animal => {
    const matchesSpecies = selectedSpecies ? animal.species === selectedSpecies : true;
    const matchesFarm = selectedFarmId ? animal.farmId === selectedFarmId : true;
    return matchesSpecies && matchesFarm;
  });

  // Get record options based on selected animal and record type
  const recordOptions = getRecordOptions(
    selectedSpecies,
    recordType
  );

  // Convert record options to dropdown items
  const recordOptionItems: DropdownItem[] = recordOptions.map(option => ({
    id: option.id,
    label: option.label,
    description: option.description,
    imageUri: option.imageUri
  }));

  // Reset selected option when animal type or record type changes
  useEffect(() => {
    setSelectedOption('');
  }, [selectedSpecies, recordType]);

  // Update next due date when record type or record date changes
  useEffect(() => {
    if (recordType === RecordType.VACCINATION) {
      // Set next vaccination date to 30 days after the record date
      const nextDate = new Date(recordDate);
      nextDate.setDate(nextDate.getDate() + VACCINATION_INTERVAL);
      setNextDueDate(nextDate);
    } else if (recordType === RecordType.GENERAL) {
      // Set next checkup date to 7 days after the record date
      const nextDate = new Date(recordDate);
      nextDate.setDate(nextDate.getDate() + HEALTH_CHECK_INTERVAL);
      setNextDueDate(nextDate);
    } else {
      // For other record types, no next date is needed
      setNextDueDate(null);
    }
  }, [recordType, recordDate]);

  // Add useEffect to handle initial setup
  useEffect(() => {
    // If no species is selected (and no preSelectedAnimal), set it to 'Cow'
    if (!selectedSpecies && !preSelectedAnimal) {
      setSelectedSpecies('Cow');
    }
  }, []);

  const handleSubmit = async () => {
    if (!selectedSpecies) {
      Alert.alert(t('common.error'), t('animals.speciesRequired'));
      return;
    }

    if (!selectedAnimal) {
      Alert.alert(t('common.error'), t('animals.selectAnimalRequired'));
      return;
    }

    if (!recordType) {
      Alert.alert(t('common.error'), t('records.recordTypeRequired'));
      return;
    }

    setIsSubmitting(true);

    try {
      // Find the selected option details
      const selectedOptionDetails = recordOptions.find(option => option.id === selectedOption);

      // Prepare record data
      const recordData = {
        animalId: selectedAnimal,
        date: recordDate.getTime(),
        type: recordType as RecordType,
        title: selectedOptionDetails
          ? `${recordType}: ${selectedOptionDetails.label}`
          : `${recordType} record`,
        description: notes, // Start with notes, add translated parts below
        symptoms: [],
      };

      let additionalDescription = '';
      if (selectedOptionDetails) {
        // Use t() for "Details:"
        // selectedOptionDetails.label and .description are assumed to be actual content here
        additionalDescription += `\n\n${t('records.detailsPrefix', { defaultValue: 'Details:' })} ${selectedOptionDetails.label} - ${selectedOptionDetails.description}`;
      }

      if (practitioner) {
        // Use t() for "Practitioner:"
        additionalDescription += `\n\n${t('records.practitionerPrefix', { defaultValue: 'Practitioner:' })} ${practitioner}`;
      }
      recordData.description += additionalDescription;

      // Add next due date information to description if applicable
      if (nextDueDate && (recordType === RecordType.VACCINATION || recordType === RecordType.GENERAL)) {
        // Use the app's current language for date formatting, similar to [id].tsx
        const currentLocale = language === 'ur' ? 'ur-PK' : language === 'pa' ? 'pa-IN' : 'en-US';
        // const formattedNextDate = nextDueDate.toLocaleDateString(currentLocale, {
        //   year: 'numeric',
        //   month: 'short',
        //   day: 'numeric',
        // });
        const formattedNextDate = formatDate(nextDueDate.getTime(), currentLocale)
        let nextDueString = '';
        if (recordType === RecordType.VACCINATION) {
          // Use t() for "Next vaccination due:"
          nextDueString = `\n\n${t('records.nextVaccinationDuePrefix', { defaultValue: 'Next vaccination due:' })} ${formattedNextDate}`;
        } else if (recordType === RecordType.GENERAL) {
          // Use t() for "Next check-up due:"
          nextDueString = `\n\n${t('records.nextCheckupDuePrefix', { defaultValue: 'Next check-up due:' })} ${formattedNextDate}`;
        }
        recordData.description += nextDueString;
      }

      // Add the record
      await addRecord(recordData);

      // Navigate directly to the animal detail screen
      router.replace(`/animals/${selectedAnimal}`);
    } catch (error) {
      console.error('Error adding record:', error);
      Alert.alert('Error', 'Failed to add record');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to start voice recording for notes
  const startRecording = async () => {
    // Show unsupported message on web platform
    if (Platform.OS === 'web') {
      Alert.alert(
        t('common.unsupported'),
        t('common.voiceRecordingWebUnsupported')
      );
      return;
    }

    try {
      setIsRecordingNotes(true);
      setIsProcessingSpeech(true);

      try {
        // Get the current app language
        const appLanguage = await getStoredLanguage();
        // Get the appropriate speech language code (ur-PK for Urdu)
        const speechLanguage = getSpeechLanguageCode(appLanguage);

        // Set the current field in the speech service
        setCurrentField('notes');

        // Start recording with the correct language
        await startSpeechRecognition({ language: speechLanguage });

        // Play feedback
        playFeedback('toggle');
      } catch (error) {
        console.error('Error starting speech recognition:', error);
        Alert.alert(t('common.error'), t('common.speechRecognitionError'));
        setIsProcessingSpeech(false);
        setIsRecordingNotes(false);
        setCurrentField(null);
      }
    } catch (error) {
      console.error('Error starting recording:', error);
      setIsProcessingSpeech(false);
      setIsRecordingNotes(false);
    }
  };

  // Function to stop voice recording
  const stopRecording = async () => {
    try {
      setIsProcessingSpeech(true);

      // Stop recording and get the transcribed text
      const transcribedText = await stopSpeechRecognition();

      // Append the transcribed text to the existing notes
      if (transcribedText) {
        setNotes(prevNotes => {
          if (prevNotes.trim()) {
            return `${prevNotes}\n${transcribedText}`;
          }
          return transcribedText;
        });
      }

      // Play feedback
      playFeedback('success');
    } catch (error) {
      console.error('Error stopping recording:', error);
      Alert.alert(t('common.error'), t('common.speechRecognitionError'));
      playFeedback('error');
    } finally {
      setIsRecordingNotes(false);
      setIsProcessingSpeech(false);
      setCurrentField(null);
    }
  };

  // Function to start voice recording for practitioner
  const startRecordingPractitioner = async () => {
    // Show unsupported message on web platform
    if (Platform.OS === 'web') {
      Alert.alert(
        t('common.unsupported'),
        t('common.voiceRecordingWebUnsupported')
      );
      return;
    }

    try {
      setIsRecordingPractitioner(true);
      setIsProcessingSpeech(true);

      try {
        // Get the current app language
        const appLanguage = await getStoredLanguage();
        // Get the appropriate speech language code
        const speechLanguage = getSpeechLanguageCode(appLanguage);

        // Set the current field in the speech service
        setCurrentField('practitioner');

        // Start recording with the correct language
        await startSpeechRecognition({ language: speechLanguage });

        // Play feedback
        playFeedback('toggle');
      } catch (error) {
        console.error('Error starting speech recognition:', error);
        Alert.alert(t('common.error'), t('common.speechRecognitionError'));
        setIsProcessingSpeech(false);
        setIsRecordingPractitioner(false);
        setCurrentField(null);
      }
    } catch (error) {
      console.error('Error starting recording:', error);
      setIsProcessingSpeech(false);
      setIsRecordingPractitioner(false);
    }
  };

  // Function to stop voice recording for practitioner
  const stopRecordingPractitioner = async () => {
    try {
      setIsProcessingSpeech(true);

      // Stop recording and get the transcribed text
      const transcribedText = await stopSpeechRecognition();

      // Set the transcribed text as the practitioner name
      if (transcribedText) {
        setPractitioner(transcribedText);
      }

      // Play feedback
      playFeedback('success');
    } catch (error) {
      console.error('Error stopping recording:', error);
      Alert.alert(t('common.error'), t('common.speechRecognitionError'));
      playFeedback('error');
    } finally {
      setIsRecordingPractitioner(false);
      setIsProcessingSpeech(false);
      setCurrentField(null);
    }
  };

  const styles = getStyles(themedColors);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen options={{
        title: t('records.addRecord'),
        headerTitleAlign: language === 'ur' ? 'center' : 'left',
        headerTitleStyle: { fontWeight: 'bold' },
        headerLeft: () => (
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={themedColors.text} />
          </TouchableOpacity>
        )
      }} />

      <ScrollView style={styles.scrollView}>
        <View style={styles.formSection}>
          {/* Farm Filter */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('farms.selectFarm')}</Text>
            </View>
            <FarmFilterDropdown
              onFarmSelect={setSelectedFarmId}
              selectedFarmId={selectedFarmId}
              includeAllOption={true}
            />
          </View>

          {/* Animal Type Selection */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('animals.type')}</Text>
            </View>
            <SpeciesFilterRow
              selectedSpecies={selectedSpecies}
              onSelectSpecies={setSelectedSpecies}
            />
          </View>

          {/* Animal Selection */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('animals.selectAnimal')}</Text>
            </View>
            <AnimalSearchDropdown
              placeholder={t('animals.selectAnimal')}
              animals={filteredAnimals}
              value={selectedAnimal}
              onSelect={setSelectedAnimal}
            />
          </View>

          {/* Date */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Calendar size={24} color={themedColors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('records.date')}</Text>
            </View>
            <DatePickerInput
              // label="Select Date"
              label=""
              value={recordDate}
              onChange={setRecordDate}
            />
          </View>

          {/* Record Type */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <FileText size={24} color={themedColors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>{t('records.recordType')}</Text>
            </View>
            <GenericDropdown
              placeholder={t('records.selectRecordType')}
              items={getRecordTypeItems(t, themedColors)}
              value={recordType}
              onSelect={(id: string) => setRecordType(id as RecordType)}
              modalTitle={t('records.selectRecordType')}
              searchPlaceholder={t('records.searchRecordTypes')}
              renderIcon={<FileType size={24} color={themedColors.textSecondary} />}
            />
          </View>

          {/* Record Options - only shown when both animal type and record type are selected */}
          {selectedSpecies && recordType && recordOptionItems.length > 0 && (
            <View style={styles.formGroup}>
              <View style={styles.labelContainer}>
                <FileText size={24} color={themedColors.primary} />
                <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
                  {getRecordDetailsLabel(recordType, t)}
                </Text>
              </View>
              <GenericDropdown
                placeholder={t(`records.select${recordType.charAt(0).toUpperCase() + recordType.slice(1)}Details`)}
                items={recordOptionItems.map(item => ({
                  ...item,
                  label: language === 'ur' ? t(`records.${item.id}`) || item.label : item.label,
                  description: language === 'ur' ? t(`records.${item.id}Description`) || item.description : item.description,
                  imageUri: item?.imageUri
                }))}
                value={selectedOption}
                onSelect={setSelectedOption}
                modalTitle={t(`records.select${recordType.charAt(0).toUpperCase() + recordType.slice(1)}Details`)}
                searchPlaceholder={t(`records.search${recordType.charAt(0).toUpperCase() + recordType.slice(1)}Details`)}
                renderIcon={<FileText size={24} color={themedColors.textSecondary} />}
                allowCustomOption={true}
                customOptionLabel={t('records.addNew', {
                  type: recordType === RecordType.VACCINATION ? t('records.vaccine') :
                    recordType === RecordType.MEDICATION ? t('records.medicine') :
                      recordType === RecordType.SURGERY ? t('records.surgery') :
                        t('records.checkup')
                })}
                onAddCustomOption={(newValue) => {
                  // Handle the new option addition here
                  setSelectedOption(newValue);
                }}
              />
            </View>
          )}

          {/* Next Due Date - only shown for Vaccination and Check-up record types */}
          {(recordType === RecordType.VACCINATION || recordType === RecordType.GENERAL) && nextDueDate && (
            <View style={styles.formGroup}>
              <View style={styles.labelContainer}>
                <Clock size={24} color={themedColors.primary} />
                <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
                  {recordType === RecordType.VACCINATION ? t('records.nextVaccinationDue') : t('records.nextCheckupDue')}
                </Text>
              </View>
              <DatePickerInput
                label=""
                value={nextDueDate}
                onChange={setNextDueDate}
              />
            </View>
          )}

          {/* Practitioner */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <Stethoscope size={24} color={isSelfTreatment ? themedColors.textSecondary : themedColors.primary} />
              <Text style={[
                styles.label,
                language === 'ur' ? styles.urduText : null,
                isSelfTreatment && styles.disabledLabel
              ]}>
                {t('records.practitioner')}
              </Text>
              <View style={styles.selfContainer}>
                <Switch
                  value={isSelfTreatment}
                  onValueChange={(newValue) => {
                    setIsSelfTreatment(newValue);
                    if (newValue) {
                      setPractitioner(''); // Clear practitioner when self is selected
                    }
                  }}
                  trackColor={{ false: themedColors.border, true: themedColors.primaryLight }}
                  thumbColor={isSelfTreatment ? themedColors.primary : '#f4f3f4'}
                />
                <Text style={styles.selfLabel}>{t('records.self')}</Text>

                {/* Voice button for practitioner - now positioned here */}
                {Platform.OS !== 'web' && !isSelfTreatment && (
                  <TouchableOpacity
                    style={[
                      styles.voiceButtonHeader,
                      isRecordingPractitioner && styles.voiceButtonRecording
                    ]}
                    onPress={() => {
                      if (isRecordingPractitioner) {
                        stopRecordingPractitioner();
                      } else {
                        startRecordingPractitioner();
                      }
                    }}
                  >
                    {isRecordingPractitioner ? (
                      <MicOff size={20} color={themedColors.error} />
                    ) : (
                      <Mic size={20} color={themedColors.primary} />
                    )}
                  </TouchableOpacity>
                )}
              </View>
            </View>
            <View style={styles.inputContainer}>
              <Input
                value={practitioner}
                onChangeText={setPractitioner}
                placeholder={t('records.practitionerPlaceholder')}
                editable={!isSelfTreatment}
                style={[
                  isSelfTreatment ? styles.disabledInput : undefined,
                  isRecordingPractitioner && styles.inputRecording
                ]}
              />
            </View>
            {isRecordingPractitioner && (
              <View style={styles.recordingIndicator}>
                <Text style={styles.recordingText}>{t('common.recording')}</Text>
              </View>
            )}
          </View>

          {/* Notes */}
          <View style={styles.formGroup}>
            <View style={styles.labelContainer}>
              <FileText size={24} color={themedColors.primary} />
              <Text style={[styles.label, language === 'ur' ? styles.urduText : null]}>
                {t('records.notes')}
              </Text>

              {/* Voice button for notes - now positioned in the header */}
              {Platform.OS !== 'web' && (
                <TouchableOpacity
                  style={[
                    styles.voiceButtonHeader,
                    isRecordingNotes && styles.voiceButtonRecording
                  ]}
                  onPress={() => {
                    if (isRecordingNotes) {
                      stopRecording();
                    } else {
                      startRecording();
                    }
                  }}
                >
                  {isRecordingNotes ? (
                    <MicOff size={20} color={themedColors.error} />
                  ) : (
                    <Mic size={20} color={themedColors.primary} />
                  )}
                </TouchableOpacity>
              )}
            </View>
            <View style={styles.inputContainer}>
              <TextInput
                style={[
                  styles.input,
                  styles.textArea,
                  isRecordingNotes && styles.inputRecording
                ]}
                value={notes}
                onChangeText={setNotes}
                placeholder={t('records.addNotes')}
                placeholderTextColor={themedColors.textSecondary}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>
            {isRecordingNotes && (
              <View style={styles.recordingIndicator}>
                <Text style={styles.recordingText}>{t('common.recording')}</Text>
              </View>
            )}
          </View>

          <Button
            title={t('records.saveRecord')}
            onPress={handleSubmit}
            isLoading={isSubmitting}
            leftIcon={<Save size={20} color="white" />}
          />

        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
  },
  formSection: {
    padding: 16,
    // backgroundColor: themedColors.card,
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: Platform.OS === 'android' ? 'top' : undefined,
  },
  animalSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: themedColors.background,
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    padding: 12,
  },
  animalSelectorText: {
    fontSize: 16,
    color: themedColors.text,
  },
  animalList: {
    backgroundColor: themedColors.background,
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    marginTop: 8,
    maxHeight: 200,
  },
  animalItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectedAnimalItem: {
    backgroundColor: themedColors.primaryLight,
  },
  animalItemText: {
    fontSize: 16,
    color: themedColors.text,
  },
  selectedAnimalItemText: {
    color: themedColors.primary,
    fontWeight: 'bold',
  },
  noAnimalsMessage: {
    padding: 16,
    alignItems: 'center',
  },
  noAnimalsText: {
    color: themedColors.textSecondary,
    fontStyle: 'italic',
  },

  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },

  selfContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto', // Push to the right
  },
  selfLabel: {
    marginLeft: 8,
    fontSize: 14,
    color: themedColors.text,
  },
  disabledInput: {
    backgroundColor: themedColors.border,
    opacity: 0.7,
  },
  disabledLabel: {
    color: themedColors.textSecondary, // This is the dark gray color from your color scheme
  },
  inputContainer: {
    position: 'relative',
    width: '100%',
  },

  input: {
    backgroundColor: themedColors.background,
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: themedColors.text,
    width: '100%',
  },

  inputRecording: {
    borderColor: themedColors.error,
    borderWidth: 1,
  },

  voiceButton: {
    position: 'absolute',
    right: 10,
    top: 10,
    padding: 8,
    borderRadius: 20,
    backgroundColor: themedColors.card,
    zIndex: 1,
  },

  voiceButtonRecording: {
    backgroundColor: themedColors.error,
  },

  recordingIndicator: {
    marginTop: 5,
    padding: 5,
    backgroundColor: themedColors.error,
    borderRadius: 4,
    alignItems: 'center',
  },

  recordingText: {
    color: themedColors.error,
    fontSize: 12,
    fontWeight: 'bold',
  },
  practitionerInputContainer: {
    position: 'relative',
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },

  voiceButtonPractitioner: {
    position: 'absolute',
    right: 10,
    padding: 8,
    borderRadius: 20,
    backgroundColor: themedColors.card,
    zIndex: 1,
  },
  // voiceButton: {
  //   marginLeft: 10,
  //   padding: 8,
  //   borderRadius: 20,
  //   flexDirection: 'row',
  //   alignItems: 'center',
  // },
  // voiceButtonRecording: {
  //   backgroundColor: colors.errorLight,
  //   paddingRight: 12,
  // },

  // textAreaRecording: {
  //   borderColor: themedColors.error,
  //   borderWidth: 1,
  // },
  voiceButtonHeader: {
    marginLeft: 'auto', // Push to the right
    padding: 8,
    borderRadius: 20,
    backgroundColor: themedColors.card,
  },

});
