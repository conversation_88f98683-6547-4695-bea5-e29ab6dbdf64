import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { Farm } from '@/types/farm';
import { useTranslation } from '@/hooks/useTranslation';
import GenericDropdown, { DropdownItem } from '@/components/GenericDropdown';
import { MapPin } from 'lucide-react-native';
import { colors } from '@/constants/colors';

interface FarmFilterDropdownProps {
  onFarmSelect: (farmId: string | null) => void;
  selectedFarmId?: string | null;
  includeAllOption?: boolean;
}

const FarmFilterDropdown: React.FC<FarmFilterDropdownProps> = ({
  onFarmSelect,
  selectedFarmId,
  includeAllOption = false,
}) => {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const { farms, fetchFarms, isLoading } = useFarmStore();
  const [farmItems, setFarmItems] = useState<DropdownItem[]>([]);

  useEffect(() => {
    if (user) {
      fetchFarms(user.id);
    }
  }, [user]);

  useEffect(() => {
    // Convert farms to dropdown items - only if farms exist
    if (farms.length === 0) {
      setFarmItems([]);
      return;
    }

    let items: DropdownItem[] = farms.map((farm) => ({
      id: farm.id,
      label: farm.name,
      description: farm.location,
      icon: <MapPin size={20} color={colors.primary} />
    }));

    // Add "All Farms" option if requested and farms exist
    if (includeAllOption) {
      items = [
        {
          id: 'all',
          label: t('farms.allFarms'),
          icon: <MapPin size={20} color={colors.primary} />
        },
        ...items
      ];
    }

    setFarmItems(items);
  }, [farms, includeAllOption, t]);

  // Handle farm selection
  const handleSelect = (farmId: string) => {
    if (farmId === 'all') {
      onFarmSelect(null);
    } else {
      onFarmSelect(farmId);
    }
  };

  // Don't render if no farms available
  if (farms.length === 0) {
    return null;
  }

  return (
    <GenericDropdown
      placeholder={selectedFarmId ? undefined : t('farms.selectFarmPlaceholder')}
      items={farmItems}
      value={selectedFarmId || (includeAllOption ? 'all' : '')}
      onSelect={handleSelect}
      modalTitle={t('farms.selectFarm')}
      searchPlaceholder={t('farms.searchFarms')}
    />
  );
};

export default FarmFilterDropdown;